# Node.js based Potree viewer with dynamic path handling
FROM node:18-alpine

# Install Docker CLI for container management
RUN apk add --no-cache docker-cli

# Set working directory
WORKDIR /app

# Install dependencies
COPY package.json package-lock.json* ./
RUN npm install

# Copy application files
COPY server.js ./
COPY viewer ./viewer

# Create data directory for mounted volumes
RUN mkdir -p /app/public/data

# Create las_laz directory for uploads
RUN mkdir -p /app/las_laz

# Expose port
EXPOSE 3000

# Start the server
CMD ["node", "server.js"]
