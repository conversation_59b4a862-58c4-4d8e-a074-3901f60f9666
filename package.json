{"name": "potree-viewer-server", "version": "1.0.0", "description": "Dynamic Potree viewer server", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "express-session": "^1.17.3", "multer": "^1.4.5-lts.1", "path": "^0.12.7", "fs": "^0.0.1-security"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["potree", "viewer", "pointcloud"], "author": "", "license": "MIT"}