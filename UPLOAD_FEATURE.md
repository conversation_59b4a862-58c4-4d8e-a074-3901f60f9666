# 📁 Upload Feature Documentation

## Overview
The upload feature allows you to upload LAS/LAZ files directly through the web interface with real-time progress tracking and automatic conversion.

## Features
- ✅ **Unlimited file size and quantity** - No restrictions on file size or number of files
- ✅ **Real-time upload progress** - Visual progress bars showing upload percentage
- ✅ **Drag & Drop support** - Drag files directly to the upload area
- ✅ **Multiple file selection** - Select and upload multiple files at once
- ✅ **Automatic conversion** - Files are automatically converted after upload
- ✅ **Real-time conversion logs** - Monitor conversion progress in real-time
- ✅ **File validation** - Only LAS/LAZ files are accepted

## How to Use

### 1. Access Upload Page
- Login to admin panel: `http://localhost:8080/` (admin/1234)
- Click "📁 Upload Files" button in the header

### 2. Upload Files
**Method 1: Drag & Drop**
- Drag LAS/LAZ files directly to the upload area
- Files will be automatically detected and listed

**Method 2: File Selection**
- Click "📂 Select Files" button
- Choose multiple LAS/LAZ files from your computer

### 3. Monitor Upload Progress
- Real-time progress bars show upload percentage for each file
- Upload status messages keep you informed

### 4. Automatic Conversion
- After successful upload, the system automatically restarts the converter
- Real-time conversion logs show the progress
- Logs refresh every 5 seconds automatically

### 5. Access Converted Files
- Return to home page to see newly converted point clouds
- Use public URLs to share: `http://localhost:8080/public/{pointcloud_name}`

## Technical Details

### File Handling
- Files are uploaded to `/app/las_laz` directory
- Original filenames are preserved
- Duplicate files will overwrite existing ones

### Conversion Process
- Upload triggers `docker restart potree-converter` command
- Converter processes all files in las_laz folder
- Output goes to `/app/output` directory

### Progress Tracking
- Uses XMLHttpRequest for upload progress monitoring
- Progress calculated as (loaded bytes / total bytes) * 100
- Real-time updates during upload

### Error Handling
- File type validation (only .las, .laz allowed)
- Upload error detection and user feedback
- Conversion status monitoring

## API Endpoints

### Upload Files
```
POST /upload
Content-Type: multipart/form-data
Authentication: Required (session-based)

Response:
{
  "success": true,
  "message": "Successfully uploaded X file(s) and started conversion",
  "files": [
    {
      "name": "filename.las",
      "size": 12345678,
      "sizeFormatted": "11.77 MB"
    }
  ]
}
```

### Check Conversion Status
```
GET /api/conversion-status
Authentication: Required (session-based)

Response:
{
  "success": true,
  "logs": [
    "=== Potree Converter Docker Container ===",
    "Found 3 LAS/LAZ files to process",
    "Processing: filename.las",
    "Completed: filename.las"
  ]
}
```

## Security Considerations
- Upload feature is protected by admin authentication
- File type validation prevents malicious uploads
- Docker socket access is limited to container restart operations
- Session-based authentication prevents unauthorized access

## Troubleshooting

### Upload Fails
- Check file format (must be .las or .laz)
- Ensure sufficient disk space
- Verify network connection

### Conversion Not Starting
- Check if potree-converter container is running
- Verify Docker socket access in docker-compose.yml
- Check container logs: `docker logs potree-converter`

### Large File Uploads
- No size limits are enforced
- Upload time depends on file size and network speed
- Monitor browser memory usage for very large files

## Browser Compatibility
- Modern browsers with XMLHttpRequest Level 2 support
- Drag & Drop API support required
- JavaScript must be enabled

## Performance Notes
- Multiple file uploads are processed sequentially
- Large files may take time to upload depending on connection
- Conversion time varies based on point cloud size and complexity
- Real-time logs refresh every 5 seconds to balance performance and updates
